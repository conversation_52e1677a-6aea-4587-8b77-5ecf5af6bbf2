import removeMarkdown from 'markdown-to-text'
import { ChatInterruptHandler } from '../message_handler/interrupt/interrupt_handler'
import { IWorkflowState } from './state'
import { BaseMessage, SystemMessage } from '@langchain/core/messages'
import logger from 'model/logger/logger'
import { LLM } from 'lib/ai/llm/llm_model'
import { Config } from 'config'
import { sleep } from 'lib/schedule/schedule'
import { chunkMarkdown } from 'lib/segment/segment'
import { catchErrorSync } from 'lib/error/catchError'
import { ChatDB, IChat } from '../database/chat'
import { ChatHistoryService } from '../chat_history/chat_history'
import { OpenAIModelName } from 'lib/ai/llm/client'

// Define message sender function type
export type MessageSenderFunction = (
  text: string,
  chat_id: string,
  user_id: string,
  round_id: string,
  noSplit?: boolean,
  short_description?: string
) => Promise<any>

interface IStreamReplyParams {
  chat_id: string
  user_id: string
  round_id: string
  content: string
  interruptHandler?: ChatInterruptHandler
  lineHandler: (line: string, isFirstSentence: boolean) => Promise<string>
  noSplit?: boolean // 是否分句
  messageSender: MessageSenderFunction
}

export interface LLMReplyBaseParam {
  state: IWorkflowState
  context: BaseMessage[] // Pre-built context/messages
  model?: OpenAIModelName
  temperature?: number
  maxTokens?: number
  short_description?: string

  promptName: string // 标识任务名称
  noTypingWaiting?: boolean // 取消模拟打字延迟
  noSplit?: boolean // 是否分句
  noInterrupt?: boolean // 不使用打断
  notCheckRepeat?: boolean // 不检查语句重复
  regenerate?: boolean // 当输出重复导致输出被舍弃的时候，是否要重新生成文本
  postReplyCallBack?: () => Promise<void> // 消息发送结束之后的回调
}

export interface LLMReplyParam extends LLMReplyBaseParam{
  messageSender: MessageSenderFunction // 发送消息函数，可以通过传入 wecom, whatsapp 处理逻辑来自定义发送逻辑
}

export class LLMReply {

  chatDBClient:ChatDB<IChat>
  chatHistoryServiceClient:ChatHistoryService

  constructor(chatDBClient:ChatDB<IChat>, chatHistoryServiceClient:ChatHistoryService) {
    this.chatDBClient = chatDBClient
    this.chatHistoryServiceClient = chatHistoryServiceClient
  }

  /**
     * 调用 LLM 进行回复
     * @param param
     */
  public async invoke(param: LLMReplyParam) {
    logger.debug({ chat_id: param.state.chat_id }, 'enter LLMNode', JSON.stringify({ chat_id: param.state.chat_id, round_id: param.state.round_id, promptName: param.promptName, user_message: param.state.userMessage }, null, 2))

    // 如果当前有新消息，把当前回复丢弃掉
    if (param.state.interruptHandler && !param.noInterrupt) {
      await param.state.interruptHandler.interruptCheck()
    }

    const llm = new LLM({
      model: param.model,
      temperature: param.temperature,
      maxTokens: param.maxTokens,
      meta: {
        promptName: param.promptName,
        chat_id: param.state.chat_id,
        round_id: param.state.round_id,
      },
    })

    const res = await this.llmReply(param, llm)

    if (!res.reply) {
      logger.warn({ chat_id: param.state.chat_id }, `信息：“${param.state.userMessage} ” 没有回复`)
    }

    // 重复重新生成
    if (param.regenerate && !res.reply && res.repeatedSentence) {
      logger.log({ chat_id: param.state.chat_id }, `重新生成回复: "${res.repeatedSentence}"`)

      // 修改系统提示词
      const regenerationContext = [...param.context]
      // 添加避免重复回复提示
      if (regenerationContext.length > 0 && regenerationContext[0].getType() === 'system') {
        const systemContent = regenerationContext[0].content as string
        regenerationContext[0] = new SystemMessage(`${systemContent}

**Important: Avoid repeating the phrase "${res.repeatedSentence}"** 可以换一种方式回复`)
      }

      const regeneratedRes = await this.llmReply(
        {
          ...param,
          context: regenerationContext
        },
        llm
      )

      return regeneratedRes.reply
    }

    if (param.postReplyCallBack) {
      await param.postReplyCallBack()
    }

    return res.reply
  }

  private async llmReply(param: LLMReplyParam, llm: LLM) {
    const replyContent = await llm.predictMessage(param.context)

    // 记录重复语句
    let hasRepeated = false
    let repeatedSentence = ''

    // 进行分句回复
    const reply = await LLMReply.splitSentencesAndReply({
      chat_id: param.state.chat_id,
      user_id: param.state.user_id,
      round_id: param.state.round_id,
      noSplit: param.noSplit,
      content: replyContent,
      interruptHandler: param.state.interruptHandler,
      messageSender: param.messageSender,
      lineHandler: async (line: string, isFirstSentence) => {
        // 如果当前有新消息，把当前回复丢弃掉
        if (param.state.interruptHandler && !param.noInterrupt) {
          await param.state.interruptHandler.interruptCheck()
        }

        // 检查人工介入
        if (await this.chatDBClient.isHumanInvolvement(param.state.chat_id)) {
          logger.trace({ chat_id: param.state.chat_id }, '人工介入')
          return ''
        }

        // 模拟人工打字
        if (!isFirstSentence && !Config.setting.localTest && !param.noTypingWaiting) {
          const typingSpeed = 1.7
          const charCount = line.length
          const delayTime = Math.floor(charCount / typingSpeed * 1000)
          await sleep(delayTime)
        }

        // 如果当前有新消息，把当前回复丢弃掉
        if (param.state.interruptHandler && !param.noInterrupt) {
          await param.state.interruptHandler.interruptCheck()
        }

        // 再次校验下 人工介入
        if (await this.chatDBClient.isHumanInvolvement(param.state.chat_id)) {
          logger.trace({ chat_id: param.state.chat_id }, 'Human involvement detected')
          return ''
        }

        // 检查是否重复
        if (!param.notCheckRepeat) {
          const isRepeated = await this.chatHistoryServiceClient.isRepeatedMsg(param.state.chat_id, line)
          if (isRepeated) {
            hasRepeated = true
            repeatedSentence = line
            logger.warn({ chat_id: param.state.chat_id }, `重复回复已丢弃: ${line}`)
            return ''
          }
        }

        // 发送消息
        await param.messageSender(line, param.state.chat_id, param.state.user_id, param.state.round_id, param.noSplit, param.short_description)

        return line
      }
    })

    return {
      reply,
      repeatedSentence
    }
  }

  /**
     * Split and stream reply text with processing
     */
  private static async splitSentencesAndReply(params: IStreamReplyParams): Promise<string> {
    let reply = ''
    let isFirstSentence = true

    // 不分句
    if (params.noSplit) {
      const processedText = this.cleanText(params.content)
      await params.lineHandler(processedText, isFirstSentence)
      return processedText
    }

    // 分句
    const sentences = this.splitIntoSentences(params.content)

    for (const sentence of sentences) {
      // 清洗文本
      const processedSentence = this.cleanText(sentence)

      if (processedSentence.trim().length === 0) {
        continue
      }

      // Process the sentence through handler
      const sent = await params.lineHandler(processedSentence, isFirstSentence)
      if (sent) {
        reply += `${sent}\n`
      }

      // Update first sentence flag
      if (isFirstSentence) {
        isFirstSentence = false
      }
    }

    return reply.trim()
  }

  /**
     * Split text into sentences
     */
  public static splitIntoSentences(text: string): string[] {
    // 1. 先按照序号分割，例如 "1. "、"2. " 等
    const regex = /\d+\.\s[^]*?(?=\d+\.\s|$)/g

    // 使用 match 方法提取所有匹配的段落
    const matchedSections = text.match(regex)

    // 清理分割结果：去除首尾空白并过滤空字符串
    const sections = matchedSections ? matchedSections.map((section) => section.trim()) : []

    let splitSentences: string[]
    if (sections.length > 1) {
      // 取出第一个序号分句之前的内容
      const preFirstSection = text.split(sections[0])[0].trim()
      if (preFirstSection) {
        splitSentences = [preFirstSection, ...sections]
      } else {
        splitSentences = sections
      }
    } else {
      // 2. 按标点分割成数组
      // 默认先按 jina 分段方式
      const [error, chunks] = catchErrorSync(() => chunkMarkdown(text))
      if (error || chunks.length <= 1) {  // 分句失败，兜底回按符号分割
        splitSentences = text
        // 英文 ? 和 ! 只有在后面紧跟空白符时才断句，其余情况不分割（避免 URL 内 ? 被拆开）
        // 中文句号、问号、叹号以及换行符按原来方式断句
          .split(/(?<=[?!])(?=\s)|(?<=[。！？\n])(?![”"'])/)
      } else {
        splitSentences = chunks
      }
    }

    const rawSentences =  splitSentences.map((s) => s.trim())
      .filter(Boolean)

    // 2. 如果句子数 < 5，原样返回
    if (rawSentences.length < 5) {
      return rawSentences
    }

    // 3. 分句数 >=5 时，两两合并
    const combined: string[] = []
    for (let i = 0; i < rawSentences.length; i += 2) {
      // 取当前句子
      let merged = rawSentences[i]
      // 如果下一句存在，就合并
      if (rawSentences[i + 1]) {
        merged += ` ${rawSentences[i + 1]}`
      }
      combined.push(merged)
    }

    return combined
  }

  public static splitIntoSentencesWithMaxSentences(text: string, maxSentences = 3): string[] {
    const splitedSentence = this.splitIntoSentences(text)
    if (splitedSentence.length <= maxSentences) {
      return splitedSentence
    }
    const refineSentences:string[] = []
    const howManySentenceMergeOne = splitedSentence.length / maxSentences + (splitedSentence.length % maxSentences > 0 ? 1 : 0)
    for (let i = 0; i < splitedSentence.length; i += howManySentenceMergeOne) {
      refineSentences.push(splitedSentence.slice(i, i + howManySentenceMergeOne).join(' '))
    }
    return refineSentences
  }

  /**
   * Clean and sanitize text
  */
  private static cleanText(sentence: string): string {
    // 移除引号
    if ((sentence.startsWith('"') && sentence.endsWith('"')) ||
          (sentence.startsWith('"') && sentence.endsWith('"'))) {
      sentence = sentence.substring(1, sentence.length - 1)
    }

    // const isMajorityChinese = RegexHelper.isMajorityChinese(sentence)
    // if (!isMajorityChinese) {
    //   return '' // 直接丢弃输出
    // }

    // const emoji = RegexHelper.extractEmoji(sentence)
    // if (emoji) {
    //   let botMessages = await ChatHistoryService.getBotMessages(chat_id)
    //   botMessages = botMessages.slice(-3)
    //   if (botMessages.some((msg) => msg.includes(emoji))) {
    //     sentence = sentence.replaceAll(emoji, '') // emoji 去重
    //   }
    // }

    sentence = removeMarkdown(sentence).replaceAll('- ', '') // 清除 markdown 格式
    sentence = sentence.replaceAll(`${Config.setting.AGENT_NAME}:`, '').replaceAll(`${Config.setting.AGENT_NAME}：`, '').replaceAll('**', '').replaceAll('客户：', '').replaceAll('手把手', '').replace('。', ' ').replace(/\[\[[^\]]*]]/g, '').trim()


    // 检查时间场景是否正确
    if (!this.checkTimeScene(sentence)) {
      return ''
    }

    // 移除句号和换行符
    sentence = sentence.replace(/[。\n]/g, ' ').trim()

    return sentence
  }

  private static checkTimeScene(message: string) {
    const currentHour = new Date().getHours()
    if (currentHour >= 5 && currentHour <= 17) {
      return !message.includes('晚安')
    } else if (currentHour >= 17 || currentHour === 0)
    {
      return !message.includes('早安') || !message.includes('早上好')
    }

    return true
  }
}