import { AzureChatOpenAI, ChatOpenAI } from '@langchain/openai'
import { ChatAlibabaTongyi } from '@langchain/community/chat_models/alibaba_tongyi'
import { Config } from '../../../config'

export const OPENAI_MODELS = [
  'gpt-5',  // 推理模型，无需入参
  'gpt-5-chat',
  'gpt-5-mini',  // 推理模型，无需入参
] as const

export type OpenAIModelName = typeof OPENAI_MODELS[number]

export function assertOpenAIModel(model: string): asserts model is OpenAIModelName {
  if (!OPENAI_MODELS.includes(model as OpenAIModelName)) {
    throw new Error(`不支持的模型 "${model}"。允许的模型有: ${OPENAI_MODELS.join(', ')}`)
  }
}

interface IOpenAIInitParams {
  model?: OpenAIModelName
  temperature?: number
  maxTokens?: number
  frequencyPenalty?: number
  reasoningEffort?: string,
  timeout?: number
}

export const CLIENT_DEFAULT_TIMEOUT = 2 * 60 * 1000  // 2 minutes

export class AzureOpenAIClient {
  public static getClient(params: IOpenAIInitParams = {}): AzureChatOpenAI {
    const {
      model = 'gpt-5-mini',
      temperature = 0,
      maxTokens = 1024,
      frequencyPenalty = 0.5,
      reasoningEffort = 'minimal', // 'minimal'|'low'|'medium'|'high'（GPT-5 额外支持 'minimal'）
      timeout = Math.max(1000, CLIENT_DEFAULT_TIMEOUT),
    } = params
    assertOpenAIModel(model)

    const isReasoningModel = [
      'gpt-5',
      'gpt-5-mini',
      'gpt-5-nano',
    ].includes(model)

    const fields: any = {
      model,
      timeout,
      // Azure 认证与端点
      azureOpenAIApiKey: Config.setting.azureOpenAI.azureOpenAIApiKey,
      azureOpenAIApiInstanceName: Config.setting.azureOpenAI.azureOpenAIApiInstanceName,
      azureOpenAIApiDeploymentName: model,
      azureOpenAIApiVersion: Config.setting.azureOpenAI.azureOpenAIApiVersion, // preview
      baseURL: Config.setting.azureOpenAI.apiBaseUrl,
      maxRetries: 1,
      // useResponsesApi: true,
      // configuration: { logLevel: 'debug' },  // OpenAI SDK 将打印请求/响应头与 body（敏感内容会部分打码）
    }

    if (isReasoningModel) {
      fields.maxCompletionTokens = Math.max(1, maxTokens + 256)
      fields.reasoning = { 'effort': reasoningEffort }
    } else {
      fields.temperature = temperature
      fields.maxTokens = maxTokens
      fields.frequencyPenalty = frequencyPenalty
    }
    return new AzureChatOpenAI(fields)
  }
}

export class CheapOpenAI {
  public static getClient(params: IOpenAIInitParams = {}): ChatOpenAI {
    const {
      model = 'gpt-5-mini',
      temperature = 0,
      maxTokens = 1024,
      frequencyPenalty = 0.5,
      reasoningEffort = 'minimal', // 'minimal'|'low'|'medium'|'high'（GPT-5 额外支持 'minimal'）
      timeout = Math.max(1000, CLIENT_DEFAULT_TIMEOUT),
    } = params
    assertOpenAIModel(model)

    const isReasoningModel = [
      'gpt-5',
      'gpt-5-mini',
      'gpt-5-nano',
    ].includes(model)

    const openaiConfig: any = {
      model,
      timeout,
      // OpenAI 认证与端点
      apiKey: Config.setting.cheapOpenAI.apiKey,
      configuration: { baseURL: Config.setting.cheapOpenAI.apiBaseUrl },
      maxRetries: 1,
    }

    if (isReasoningModel) {
      openaiConfig.maxCompletionTokens = Math.max(1, maxTokens + 256)
      openaiConfig.reasoning = { 'effort': reasoningEffort }
    } else {
      openaiConfig.temperature = temperature
      openaiConfig.maxTokens = maxTokens
      openaiConfig.frequencyPenalty = frequencyPenalty
    }
    return new ChatOpenAI(openaiConfig)
  }
}

export class OpenAIClient {
  public static getClient(params: IOpenAIInitParams = {}): ChatOpenAI {
    const {
      model = 'gpt-5-mini',
      temperature = 0,
      maxTokens = 1024,
      frequencyPenalty = 0.5,
      reasoningEffort = 'minimal', // 'minimal'|'low'|'medium'|'high'（GPT-5 额外支持 'minimal'）
      timeout = Math.max(1000, CLIENT_DEFAULT_TIMEOUT),
    } = params
    assertOpenAIModel(model)

    const isReasoningModel = [
      'gpt-5',
      'gpt-5-mini',
      'gpt-5-nano',
    ].includes(model)

    const openaiConfig: any = {
      model,
      timeout,
      // OpenAI 认证与端点
      apiKey: Config.setting.openai.apiKeys[0],
      configuration: { baseURL: Config.setting.openai.apiBaseUrl },
      maxRetries: 1,
    }

    if (isReasoningModel) {
      openaiConfig.maxCompletionTokens = Math.max(1, maxTokens + 256)
      openaiConfig.reasoning = { 'effort': reasoningEffort }
    } else {
      openaiConfig.temperature = temperature
      openaiConfig.maxTokens = maxTokens
      openaiConfig.frequencyPenalty = frequencyPenalty
    }
    return new ChatOpenAI(openaiConfig)
  }
}

export class StableClaude {
  public static getClient(model = 'claude-3-5-sonnet-20241022', temperature = 0): ChatOpenAI {
    return new ChatOpenAI({
      model: model,
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      maxRetries: 1,
      openAIApiKey: Config.setting.stableClaude.apiKey,
      configuration: {
        baseURL: Config.setting.stableClaude.apiBaseUrl
      },
    })
  }
}

export class MiTaAI {
  public static getClient(model: 'concise' | 'detail' | 'research', temperature = 0): ChatOpenAI {
    return new ChatOpenAI({
      model: model,
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      openAIApiKey: Config.setting.cheapOpenAI.apiKey,
      configuration: {
        baseURL: 'http://112.124.32.162:8000/v1',
        defaultHeaders: {
          Authorization: 'Bearer 61638845b28fa859c374a79f-0abc662597fb4d4ca498a786cbffb761'
        }
      },
    })
  }
}

export class QwenMax {
  public static getClient(temperature = 0) {
    return new ChatAlibabaTongyi({
      alibabaApiKey: Config.setting.qwen.apiKey,
      temperature: temperature,
      model: 'qwen-max',
    })
  }
}

export class PerplexityAI {
  public static getClient(temperature = 0) {
    return new ChatOpenAI({
      model: 'llama-3-sonar-large-32k-online',
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      openAIApiKey: '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',
      configuration: {
        baseURL: 'https://api.perplexity.ai'
      },
    })
  }
}