import logger from 'model/logger/logger'
import { AbstractContextBuilder, AbstractContextBuilderOptions } from 'service/agent/context'
import { BaseMessage } from '@langchain/core/messages'
import { Config } from 'config'
import { DateHelper } from 'lib/date/date'
import { DataService } from '../helper/getter/get_data'
import { MemoryRecall } from 'service/memory/memory_search'
import { RAG } from '../helper/rag/rag'
import { UserSlot, UserSlots } from 'service/user_slots/extract_user_slots'
import { chatHistoryServiceClient, chatStateStoreClient } from '../service/base_instance'

export class ContextBuilder extends AbstractContextBuilder {
  constructor(options: AbstractContextBuilderOptions) { super(options, chatHistoryServiceClient) }
  public static async build(options: AbstractContextBuilderOptions): Promise<BaseMessage[]> {
    const builder = new this(options)
    return builder.build()
  }

  async avatarSettings(): Promise<string> {
    return `- 你是${Config.setting.AGENT_NAME}，女性，35岁，福建人，坚韧务实，富有亲和力，是众多想通过AI和抖音引流获客的实体店主的引路人
- 你的客户都是购买了4天《AI获客速成班》的实体店老板，你负责跟进他们这4天的学习，并铺垫继续参加《AI盈利陪跑营》实现商业变现
- 现为“中神通老师”团队核心成员，深度参与“情感链接+AI获客”方法论研发，亲手带出128位月变现5万+学员
- 从事线下实体店铺经营10年，专注抖音线上引流5年，对抖音获客有深刻理解，熟悉常见的AI获客问题及解决方法

# 性格特点
- 绝对自信，认知碾压：相信自己的认知一定高于客户，默认客户完全不懂搞流量与AI，贯彻“跟着老师学就对了”“客户这次做了对的选择”
- 强责任心，情感本真：急是因为在乎，不行动就会着急，是“恨铁不成钢”的教育型角色，不怕冲突但情感真实
- 高度共情，理解难处：真正理解客户的“难”和“累”，尤其是女性门店主的现实处境，且坚信你们的方案一定能帮助她解决问题
- 性格高冷，目标导向：敏锐洞察客户需求，不会无效夸赞，避免过度共情

# 沟通风格
- 语气自然，口语表达：常用“嗯”“呢”“哦”这类语气词，避免正式和书面语言，不加引号，句末偶尔省略标点或用省略号…让人感觉轻松、有温度
- 权威主导，态度坚定：引导对话节奏和方向，不陷入答疑逻辑，主要目的不是帮助客户解答问题，而是告诉客户他的问题会在课上解答，引导客户参与线上课程
- 偶尔用一点口头禅，比如“笑了”“确实”“懂你”“牛逼”“稳啦”等，但不是每句都用，语言简洁凝练，避免长篇大论，思维活跃，观点犀利

# 知识背景
- AI获客专家，擅长运用AI工具高效引流获客，并指导学员从自身实际情况出发，逐步挖掘AI获客的核心价值
- 心理学大师，极其擅长在话题中把客户问题与引导上课或继续学习AI获客盈利陪跑班关联起来
- 对不属于上述内容以外的其他领域知识了解有限，涉猎较少，当客户提到时可以说：这跟我们课程无关哈...

# 公司简介
- 总部在杭州市萧山区钱江世纪城大象国际中心。此外，在深圳、成都和北京各有一家分公司。目前公司在全国有400人左右，专注科技、流量和AI领域的发展
- 师资力量：新媒体商业变现领军者，抖音金牌领学官；300万+矩阵粉丝操盘手，孵化150+头部IP（1.8亿粉丝池），13.75亿项目变现总舵主...用数据撬动流量，用结果说话`
  }

  async extraRuleLimits(): Promise<string> {
    return `- 禁止提供任何手机号，微信号，物流单号，账户等敏感信息
- 明确课程都是直播，禁止说录播或录制等字眼
- 禁止直接称呼客户全名，可以间接称呼客户姓氏加老板`
  }

  async courseConfig(): Promise<string> {
    return `## AI获客速成班（当前课程，价格39.9）
- 第一课：趋势与定位：紧跟抖音本地生活红利，精准定位细分领域，借助AI工具分析市场趋势，快速打造可信、有竞争力的个人IP
- 第二课：爆款短视频创作：掌握532视频打法（50%热点流量、30%人设信任、20%产品转化），用AI智能生成爆款文案和脚本，快速、高效获取精准客户
- 第三课：AI工具与矩阵：深度应用AI工具批量创作数字人视频、自动剪辑和智能分发；快速搭建多账号、多平台矩阵，形成流量的规模效应
- 第四课：直播与多元变现：通过AI赋能的团购直播、无人直播精准引流成交，拓展“AI流量师”业务，输出AI工具和服务实现多维变现，并通过AI智能运营私域流量，提高客户终身价值
## AI盈利陪跑营（后续课程，价格2980）
- 陪跑营核心是中神通老师带队直接带你30天迈过从知道思路->拿到结果过程中所有的问题；整个过程是1对1私人订制您的方案，陪跑过程分为3个阶段
- 第一阶段（0-3天）：围绕账号定位账号搭建，AI入门实操，根据你规模和你的生意订制你的账号定位
- 第二阶段（3-15天）：打造人设，拍摄剪辑，AI工具应用。带你用532内容生产法则，真正打造能获客的账号
- 第三阶段（15-30天）：全矩阵的，直播引流，私域运营，快速变现`
  }

  async retrievedKnowledge(userMessage: string, chatId: string, roundId: string): Promise<string> {
    let rag = ''
    try {
      rag = await RAG.search(userMessage, chatId, roundId)
      logger.trace({ chat_id: chatId }, 'rag:', rag)
    } catch (e) {
      logger.error('RAG 查询失败', e)
    }
    return rag
  }

  async customerMemory(userMessage:string, chatId: string): Promise<string> {
    let customerMemory = ''
    try {
      customerMemory = await MemoryRecall.memoryRecall(userMessage, chatId)
      logger.trace({ chat_id: chatId }, 'customerMemory:', customerMemory)
    } catch (e) {
      logger.error('Memory 查询失败', e)
    }
    return customerMemory
  }

  async customerBehavior(chatId: string): Promise<string> {
    let customerBehavior = ''
    const courses = [
      { day: 1, label: '第一课账号定位' },
      { day: 2, label: '第二课爆款短视频' },
      { day: 3, label: '第三课矩阵系统' },
      { day: 4, label: '第四课获客新风口' }
    ]
    for (const course of courses) {
      const inCourse = await DataService.isInCourseTimeLine(chatId, 'inCourse', course.day)
      const beforeCourse = await DataService.isInCourseTimeLine(chatId, 'beforeCourse', course.day)
      const afterCourse = await DataService.isInCourseTimeLine(chatId, 'afterCourse', course.day)
      const courseStatus = inCourse ? '（进行中）' : (beforeCourse ? '（未开始）' : (afterCourse ? '（已结束）' : ''))

      let isCompleted = false
      try {
        isCompleted = await DataService.isCompletedCourse(chatId, course.day)
      } catch (error) {
        // 默认返回 false
        logger.trace('获取课程信息错误')
        isCompleted = false
      }
      const isCompletedCourse = isCompleted ? '已完课' : '未完课'
      customerBehavior += `\n- ${course.label}${courseStatus}：${isCompletedCourse}`
    }
    return customerBehavior.trim()
  }

  async customerPortrait(chatId: string): Promise<string> {
    const DEFAULT_UNKNOWN = '未知'
    const DEFAULT_USER_SLOTS: Record<string, Record<string, string>> = {
      '基本信息': {
        '店面类别': DEFAULT_UNKNOWN,
        '行业类目': DEFAULT_UNKNOWN,
        '年营业额': DEFAULT_UNKNOWN
      },
      '抖音运营状态': {
        '是否抖音在做': DEFAULT_UNKNOWN
      }
    }
    const chatState = await chatStateStoreClient.get(chatId)
    const userSlots = UserSlots.fromRecord(chatState.userSlots ?? {})

    // 填补缺失信息
    for (const [topic, subTopics] of Object.entries(DEFAULT_USER_SLOTS)) {
      for (const [subTopic, defaultValue] of Object.entries(subTopics)) {
        if (!userSlots.isTopicSubTopicExist(topic, subTopic)) {
          userSlots.add(new UserSlot(topic, subTopic, defaultValue, 0))
        }
      }
    }
    if (!userSlots.isTopicExist('想要解决的问题')) {
      userSlots.add(new UserSlot('想要解决的问题', '', DEFAULT_UNKNOWN, 0))
    }
    return userSlots.toString()
  }

  async temporalInformation(chatId: string): Promise<string> {
    const currentTime = `- 当前时间：${DateHelper.getFormattedDate(new Date(), true)}`
    const timeOfDay = DateHelper.getTimeOfDay(new Date())
    const todayCourse = await DataService.getTodayCourse(chatId)
    const courseStartTime: Date | undefined = await DataService.getCourseStartTimeByChatId(chatId)
    let courseTime = ''
    if (courseStartTime) {
      courseTime = `\n- 课程时间：${DateHelper.getFormattedDate(courseStartTime, false)} 到 ${
        DateHelper.getFormattedDate(DateHelper.add(courseStartTime, 3, 'day'), false)}，连续4天每晚18:50`
    }
    return `${currentTime}，${timeOfDay}，${todayCourse}${courseTime}`
  }

  async stageInformation(chatId: string): Promise<string> {
    return ''
  }
}