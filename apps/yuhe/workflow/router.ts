import logger from 'model/logger/logger'
import { IWorkflowState } from 'service/llm/state'
import { Node, WorkFlowNode } from './nodes/node'
import { LLM } from 'lib/ai/llm/llm_model'
import { HumanTransfer, HumanTransferType } from '../human_transfer/human_transfer'
import { IEventType } from 'model/logger/data_driven'
import { catchError } from 'lib/error/catchError'
import { JuziAPI } from 'model/juzi/api'
import { Config } from 'config'
import { sleep } from 'lib/schedule/schedule'
import { IWecomMsgType } from 'model/juzi/type'
import { DateHelper } from 'lib/date/date'
import { EventHandler } from '../client/event_handler'
import { DataService } from '../helper/getter/get_data'
import { PrismaMongoClient } from '../database/prisma'
import { RegexHelper } from 'lib/regex/regex'
import { checkRobotDetection } from 'service/agent/utils'
import { getPrompt } from 'service/agent/prompt'
import { IChattingFlag } from '../state/user_flags'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../service/base_instance'
import { wecomCommonMessageSender, wecomMessageSender } from '../service/send_message_instance'
import { eventTrackClient } from '../service/event_track_instance'
import { humanTransferClient } from '../service/human_transfer_instance'
import { talkCounter } from '../prometheus/client'
import { FreeTalk } from 'service/agent/freetalk'
import { ContextBuilder } from './context'
import { replyClient } from '../service/instance'
import { stageFilter } from './meta_action/stage'

export class FreeTalkNode extends WorkFlowNode {
  public static async invoke(state: IWorkflowState) {
    talkCounter.labels({ bot_id: Config.setting.wechatConfig?.name || Config.setting.wechatConfig?.id }).inc(1)
    const freeTalk = new FreeTalk(chatHistoryServiceClient, ContextBuilder, eventTrackClient, replyClient, stageFilter)
    return await freeTalk.invoke(state)
  }
}

export class Router {
  /**
   * 根据客户消息进行路由，特别注意这里的路由要写的 特定情况才能跳转，不能太通用，不然容易路由到错误的节点
   * 返回 End, 表示不执行任何节点逻辑
   * @param state
   */
  public static async route(state: IWorkflowState): Promise<Node> {
    const chatId = state.chat_id
    const userId = state.user_id
    const roundId = state.round_id
    const userMessage = state.userMessage
    const currentTime = await DataService.getCurrentTime(chatId)
    const mongoClient = PrismaMongoClient.getInstance()
    const day = currentTime.day
    if (!userMessage) return Node.Dummy

    // 刷新到完课状态
    if (0 < day && day < 5 && DateHelper.isTimeAfter(currentTime.time, '18:50:00')) {
      await DataService.isAttendCourse(chatId, day)
      await DataService.isCompletedCourse(chatId, day)
    }

    // 废话过滤
    const isChatter = RegexHelper.filterChatter(userMessage)
    const beforeCourse3 = await DataService.isInCourseTimeLine(chatId, 'beforeCourse', 3)
    if (isChatter && beforeCourse3) return Node.DummyEnd

    // 客户识别AI检查
    const isRobotDetection = await checkRobotDetection(chatStateStoreClient, humanTransferClient, chatId, roundId, userId, userMessage)
    if (isRobotDetection) return Node.DummyEnd

    // 账号截图诊断
    const isDouyinImage = this.checkDouyinImage(userMessage)
    if (isDouyinImage) return Node.DouyinAnalysis

    // 索要完课礼检查
    const isRewardAsking = this.checkRewardAsking(userMessage)
    if (isRewardAsking) return Node.SendFile

    // 第一天作业打卡
    const isHomework1 = this.checkHomework1(userMessage)
    if (isHomework1) return Node.Homework1

    // 第二天作业打卡
    const isHomework2 = this.checkHomework2(userMessage)
    if (isHomework2) return Node.Homework2

    // 支付成功检查
    const isPaymentImage = this.checkPaymentImage(userMessage)
    const isPaid = await DataService.isPaidSystemCourse(chatId)

    if (isPaymentImage && !isPaid) {
      // 支付成功提醒
      const courseNo = await chatDBClient.getCourseNo(chatId) || 2025
      const phone = await chatDBClient.getPhone(chatId)
      await mongoClient.chat.update({ where:{ id:chatId }, data:{ pay_time:new Date() } })
      await chatStateStoreClient.update(chatId, { state: { is_complete_payment: true } })
      eventTrackClient.track(chatId, IEventType.PaymentComplete, { 'payment_type': 'offline' })
      await HumanTransfer.transfer(chatId, userId, HumanTransferType.PaidCourse, 'onlyNotify', `线下支付，手机：${phone}`)

      if (!['订单号', '交易单号'].some((name) => userMessage.includes(name))) {
        await wecomMessageSender.sendById({ user_id: userId, chat_id: chatId, ai_msg: '麻烦再发一下带有订单号或者交易单号的截图哈，回头财务好核对付款信息以免后面把咱们遗漏' }, { round_id: roundId })
        await sleep(2000)
        await wecomMessageSender.sendById({
          user_id: userId,
          chat_id: chatId,
          ai_msg: '[示例订单编号截图]',
          send_msg: {
            type: IWecomMsgType.Image,
            url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/yuhe/sop/%E8%AE%A2%E5%8D%95%E7%BC%96%E5%8F%B7%E6%88%AA%E5%9B%BE.jpg',
          },
        })
      }

      // 拉群
      try {
        await EventHandler.inviteToGroup(chatId, userId)
        await catchError(JuziAPI.updateUserAlias(Config.setting?.wechatConfig?.id as string, userId, `已付${courseNo.toString(10)}`))
      } catch (e) {
        logger.error('拉群失败', e)
        // 通知进群失败
        await HumanTransfer.transfer(chatId, userId, HumanTransferType.FailedToJoinGroup, true)
      }
      return Node.DummyEnd
    }

    // 意图分类路由
    return await this.routeByCategory(userMessage, chatId, userId, roundId)
  }

  static checkDouyinImage(userMessage: string): boolean {
    if (['不是抖音首页', '并非抖音首页', '【普通图片】'].some((item) => userMessage.includes(item))) {
      return false
    }
    return userMessage.includes('【社交媒体首页截图】')
  }

  static checkRewardAsking(userMessage: string): boolean {
    const KEYWORDS = ['完课礼', '定位', '指南', '爆单', '祝福', '好评礼']
    return userMessage.split('\n').some((subItem) => KEYWORDS.includes(subItem.trim()))
  }

  static checkHomework1(userMessage: string): boolean {
    return ['商业定位', '内容定位', '人设定位', '你想一个月通过抖音赚多少钱'].some((item) => userMessage.includes(item))
  }

  static checkHomework2(userMessage: string): boolean {
    return ['你的身份', '你打算做什么', '提出具体要求'].some((item) => userMessage.includes(item))
  }

  static checkPaymentImage(userMessage: string): boolean {
    return userMessage.includes('图片') &&
      ['2980', '2975'].some((amount) => userMessage.includes(amount)) &&
      ['支付', '交易', '成功', '完成'].some((result) => userMessage.includes(result)) &&
      // ['宇合传媒', '星河披星', '星河交辉', '星河星辰', '星河众星', '星河拱月', '获客盈利', '传媒', '中神通', '银河商学', '星河觉醒'].some((name) => userMessage.includes(name)) &&
      !['最高500元', '交易已超额', '请降低金额', '或下月再付', '当前商户存在异常', '暂时无法支付', '限额'].some((amount) => userMessage.includes(amount))
  }

  // 意图分类路由
  private static async routeByCategory(userMessage: string, chat_id: string, user_id: string, round_id: string): Promise<Node> {
    const category = await Router.classify(userMessage, chat_id, round_id)
    const beforeCourse1 = await DataService.isInCourseTimeLine(chat_id, 'beforeCourse', 1)
    const afterCourse1Sales = await DataService.isInCourseTimeLine(chat_id, 'afterSales', 1)
    const afterCourse2Sales = await DataService.isInCourseTimeLine(chat_id, 'afterSales', 2)
    const chatState = await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)

    if (category === 1 && afterCourse1Sales) {
      return Node.SendFile
    } else if (category === 2 && afterCourse1Sales) {
      return Node.Homework1
    } else if (category === 3 && afterCourse2Sales) {
      return Node.Homework2
    } else if (category === 4 && !beforeCourse1) {
      if (chatState.is_bind_phone || chatState.is_complete_payment) {
        return Node.Dummy
      }
      await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.ProblemSolving, 'onlyNotify', `客户：${userMessage}`)
      const phoneNumber = await chatDBClient.getPhone(chat_id)
      if (phoneNumber) { await DataService.bindPhone(chat_id, phoneNumber) }
      const message = phoneNumber ? `用这个手机号登录哈，老师帮你开权限了\n${phoneNumber}` : '麻烦提供一下手机号哈，这边后台帮你开权限'
      await wecomCommonMessageSender.sendText(chat_id, {
        text: message,
        description: '客户无法看课，给解决方案'
      })
      await chatStateStoreClient.update(chat_id, { state:<IChattingFlag>{ is_bind_phone: true } })
      return Node.DummyEnd
    } else if (category === 5) {
      await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.ProblemSolving, 'onlyNotify', `客户：${userMessage}`)
    } else if (category === 6) {
      await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.ProblemSolving, true, `客户：${userMessage}`)
      return Node.DummyEnd
    }
    return Node.Dummy
  }

  public static async classify(userMessage: string, chat_id: string, round_id: string) {
    const routerPrompt = await getPrompt('free-route')
    const routingNodes = `1. 发送资料：客户消息为索要完课礼/课程回放/好评礼/作业或其他课程相关资料时，判断前请不要进行过度推理
  - 例如：“老师要完课礼”“有回放吗”“已好评[强]”“客户发送某平台对课程商品好评的图片”“昨天留的什么作业呀”“第一课作业是什么”，客户单独发送要完课礼的暗号“定位”“指南”“爆单”“祝福”
  - 注意：客户说“给我发个定位吧”不属于暗号，不属于这个节点，客户要直播链接不属于这个节点

2. 第一课作业：客户消息包含商业定位，内容定位，人设定位，目标客户，你想一个月通过抖音赚多少钱这几项内容时，分类到这个节点
  - 例如：客户可能会直接发送对上面这些问题的回复，比如“团购，直播，专业，同城，比现在好都行，店里面有生意都行”这样的模糊回复也属于这个节点
  - 注意：如果客户发送姓名称呼，个人实体店/连锁店，行业类目，年营业额，是否抖音在做，所在城市等相关内容时，不属于这个节点

3. 第二课作业：客户消息同时包含“客户的身份”“客户打算做什么”“提出具体要求”相关内容时，分类到这个节点
  - 例如：我是在上海开健身房的，我想通过短视频吸引更多客户到店，请根据目前抖音比较火的同类视频，帮我创作5条能吸引同城客户的方案

4. 看课问题：客户明确表示无法进入直播，无权限观看，无法看录播等看课相关问题时进入此节点
  - 注意：网络问题，卡顿，陪跑群问题等跟看课无关问题不属于这个节点，不要过度推理

5. 异常问题：客户表示投诉，辱骂，退款，无法下单/支付，支付截图，已经付款，延期上课，更换课程时间或想来公司现场看看等问题时进入此节点
  - 例如：“你们的产品是骗人的”“我要投诉”“我要退款/退费/退课”“我想上下一期课程”“付了”

6. 人工处理：客户消息仅为直播链接失效，付款限额，付款失败时进入此节点，其他特殊问题属于节点5`
    const output = await LLM.predict(
      routerPrompt, {
        responseJSON: true,
        meta: {
          promptName: 'router',
          chat_id: chat_id,
          round_id: round_id,
        } }, {
        routingNodes: routingNodes,
        customerMessage: userMessage,
      })
    let answer: number = 0
    try {
      const parsedOutput = JSON.parse(output)
      answer = parsedOutput.answer
    } catch (error) {
      logger.error('Router 解析 JSON 失败:', error)
    }
    return answer || 0
  }
}