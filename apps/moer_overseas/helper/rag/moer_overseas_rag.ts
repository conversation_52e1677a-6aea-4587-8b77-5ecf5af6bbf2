import { RAGHelper } from 'service/rag/rag'
import logger from 'model/logger/logger'
import ElasticSearchService, { IElasticEmbeddingRes } from 'model/elastic_search/elastic_search'
import { DateHelper } from 'lib/date/date'
import { DataService } from '../getter/get_data'
import { ragHelperClient } from '../../service/instance'

/**
 * Moer 通用 RAG
 */
export class RAG {
  public static index = 'moer_rag_2_2048d'
  public static nonRAGMatchIndex = 'moer_non_rag_queries_2048d'


  public static async search(inputQuery: string, chat_id: string, round_id: string) {
    const maxQANum = 5
    inputQuery = await ragHelperClient.mergeQuery(inputQuery, chat_id)
    const isNeedRAG = await this.isNeedRAG(inputQuery, chat_id)
    if (!isNeedRAG) {
      logger.trace({ chat_id: chat_id }, '当前问题无需rag:', inputQuery)
      return ''
    }

    const topic = ['21天系统班', '冥想入门营', '能量测评', '墨尔冥想app', '财富果园']
    const subQueries = await ragHelperClient.queryReWriteWithChatHistory(inputQuery, topic, chat_id, round_id)

    const embeddingResults = await Promise.all(subQueries.map((subQuery) =>  this.embeddingSearch(chat_id, subQuery)))

    // 获取检索的 QA 对
    const retrievedQAs = RAGHelper.removeDuplicateQA(embeddingResults
      .flat()
      .map((item) => {
        return {
          q: item.pageContent,
          a: item.metadata.a
        }
      }))

    let reRankResults = await RAGHelper.reRank(subQueries.join(), retrievedQAs, { top_n: maxQANum, threshold: 0.1 })
    logger.trace('reRank Results:', JSON.stringify(reRankResults, null, 2))


    // 限制召回结果数
    if (reRankResults.length > maxQANum) {
      reRankResults = reRankResults.slice(0, maxQANum)
    }

    // 移除答案中的多余换行
    reRankResults = reRankResults.map((qa) => {
      qa.a = this.removeDuplicateLineBreak(qa.a)
      return qa
    })
    let ragContext = reRankResults.map((qa) =>  `- 问题：${qa.q}\n  - 答案：${qa.a}`).join('\n')

    if (/.*?_\w{4}\.\w+/.test(ragContext)) { // RAG 包含文件
      ragContext = `下面答案中如果提到文件资源为 [文件名称] 格式，请严格按照原始文件名称输出，不要添加额外的文件或信息，并且只参考补充信息中提到的文件资源
${ragContext}`
    }
    return ragContext
  }

  private static async embeddingSearch(chat_id: string, query: string): Promise<IElasticEmbeddingRes[]> {
    // 获取要查询的文档
    const queryDocs = await this.getQueryDocs(chat_id)

    const supportVersion = '国内版本'

    // 构建查询 filter
    const filter = {
      bool:{
        must:[
          {
            terms: {
              'metadata.doc': queryDocs
            }
          }
        ],
        should: [
          { term: { 'metadata.supportVersion': supportVersion } },
          { bool: { must_not: { exists: { field: 'metadata.supportVersion' } } } }
        ],
        minimum_should_match: 1
      }

    }

    // Embedding Search
    return await ElasticSearchService.embeddingSearch(
      this.index,
      query,
      10,
      0.74,
      filter
    )
  }

  private static async getQueryDocs(chat_id: string): Promise<string[]> {
    // 公共文档
    const commonDocs = [
      '冥想问题.xlsx',
      '常规问题全局(海外).xlsx',
      '系统班全通班逐字稿',
      '课后问题回访FAQ.docx',
    ]

    // 进量期间补充文档
    const additionalNotCourseWeekDocs = [
      '开营班会',
    ]

    const additionalCourseDay3Doc = [
      '常规问题周三八点前(海外).xlsx',
    ]

    // 上课周，课程相关的文档，根据天数逐步增加
    const courseDayDocs = {
      day1: ['第一天课程-情绪减压.docx'],
      day2: ['第二天课程-财富果园.docx'],
      day3: ['第三天课程-效能提升.docx'],
    }

    // 销售相关的额外文档
    const saleAdditionalDocs = [
      '销售文档',
      '销售问题(海外).xlsx',
    ]

    // 非课程周的文档列表
    const notCourseWeekDocs = [
      ...commonDocs,
      ...additionalCourseDay3Doc,
      ...additionalNotCourseWeekDocs,
    ]

    // 课程周第一天的文档列表
    const courseWeekDay1Docs = [
      ...commonDocs,
      ...additionalCourseDay3Doc,
      ...courseDayDocs.day1,
    ]

    // 课程周第二天的文档列表
    const courseWeekDay2Docs = [
      ...commonDocs,
      ...additionalCourseDay3Doc,
      ...courseDayDocs.day1,
      ...courseDayDocs.day2,
    ]

    // 课程周第三天的文档列表
    const courseWeekDay3Docs = [
      ...commonDocs,
      ...additionalCourseDay3Doc,
      ...courseDayDocs.day1,
      ...courseDayDocs.day2,
      ...courseDayDocs.day3,
    ]

    // 销售相关的文档列表
    const saleRagDocList = [
      ...commonDocs,
      ...courseDayDocs.day1,
      ...courseDayDocs.day2,
      ...courseDayDocs.day3,
      ...saleAdditionalDocs,
    ]

    // 定义天数到文档列表的映射
    const dayToDocMap:Record<number, string[]> = {
      1: courseWeekDay1Docs,
      2: courseWeekDay2Docs,
      3: courseWeekDay3Docs, // 需要特殊处理
      4: saleRagDocList,
      5: saleRagDocList,
    }

    try {
      const { post_course_week, is_course_week, day, time } = await DataService.getCurrentTime(chat_id)

      // 课程周后
      if (post_course_week) {
        return saleRagDocList
      }

      // 非课程周后，且非课程周，使用第一周的文档列表
      if (!is_course_week) {
        return notCourseWeekDocs
      }

      // 课程周，根据当前天数决定文档列表
      if (day === 3) {
        // 第三天，需要根据时间决定文档列表
        if (DateHelper.isTimeAfter(time, '20:00:00')) {
          return saleRagDocList
        }

        return courseWeekDay3Docs
      } else {
        // 其他天数，从映射中获取对应的文档列表
        return dayToDocMap[day] || saleRagDocList
      }
    } catch (e) {
      logger.error('获取查询列表失败:', e)

      return saleRagDocList
    }
  }

  private static async isNeedRAG(inputQuery: string, chat_id: string) {
    const queryParts = inputQuery.split(/[，。\s]+/).filter((part) => part.trim() !== '')
    const unmatchedParts: string[] = []

    for (const part of queryParts) {
      const ragResults = await ElasticSearchService.embeddingSearch(this.nonRAGMatchIndex, part, 1, 0.8)

      if (ragResults.length === 0) {
        // 没有搜索到了，输出，后面要rag
        unmatchedParts.push(part)
      } else {
        logger.trace({ chat_id: chat_id }, `被删除的部分query "${part}":`, ragResults)
      }
    }

    return unmatchedParts.length > 0 && RAGHelper.isNeedRAG(unmatchedParts.join())
  }

  private static removeDuplicateLineBreak(input: string) {
    return input.replace(/\n+/g, '\n')
  }
}