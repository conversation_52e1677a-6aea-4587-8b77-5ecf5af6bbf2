'use server'

import { AdminPrismaMongoClient } from '@/lib/prisma'
import { chat } from 'moer_overseas/prisma_client/client'
import { Node } from 'moer_overseas/workflow/node'
import { contentWithFrequency } from 'service/local_cache/type'
import { MoerUserData } from '../type/user'
import dayjs from 'dayjs'

export async function queryChats(nameOrPhone:string, courseNo?:number):Promise<MoerUserData[]> {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasInstance()
  const queryNameResult = await mongoClient.chat.findRaw({
    filter: {
      $and: [
        { course_no: courseNo },
        {
          $or: [
            { _id: nameOrPhone }, // MongoDB 的主键字段通常是 _id
            { 'contact.wx_name': { $regex: nameOrPhone, $options: 'i' } },
            { 'chat_state.userSlots.phoneNumber': nameOrPhone }
          ]
        }
      ]
    },
    options: {
      limit: 40,
      sort: {
        created_at: -1
      }
    }
  })
  if (!queryNameResult) {
    throw ('error')
  }
  const result = queryNameResult as unknown as any[]
  for (let i = 0; i < result.length; i++) {
    result[i].id = result[i]['_id']
  }

  return result.map((item) => transferChatIntoUserData(item))
}

export async function queryDefaultChats():Promise<MoerUserData[]> {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasInstance()
  const queryNameResult = await mongoClient.chat.findMany({
    take:10,
    orderBy: {
      created_at: 'desc' // 按照 create_at 降序排列
    },
  })
  return queryNameResult.map((item) => transferChatIntoUserData(item))
}

export async function queryChatById(id:string):Promise<MoerUserData | null> {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasInstance()
  const result = await mongoClient.chat.findFirst({ where:{
    id
  } })
  if (result) {
    return transferChatIntoUserData(result)
  } else {
    return null
  }
}

export async function queryChatsWithoutAi(courseNo?:number): Promise<MoerUserData[]> {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasInstance()
  const result = await mongoClient.chat.findMany({ where:{
    is_human_involved:true,
    course_no:courseNo
  },
  take:courseNo ? undefined : 50,
  orderBy:{
    created_at:'desc'
  }
  })
  return result.map((item) => transferChatIntoUserData(item))
}

export async function queryChatsWithoutPhone(courseNo?:number):Promise<MoerUserData[]> {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasInstance()
  const result = await mongoClient.chat.findRaw({
    filter: { 'chat_state.userSlots.phoneNumber': { $exists: false }, course_no: courseNo },
    options: {
      limit: 40,
      sort: {
        created_at: -1
      }
    } }) as unknown as any[]
  for (let i = 0; i < result.length; i++) {
    result[i].id = result[i]['_id']
  }
  return (result as chat[]).map((item) => transferChatIntoUserData(item))
}

export async function changeIsHumanInvolved(chatId:string, isHumanInvolved:boolean) {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data: { is_human_involved:isHumanInvolved } })
}

export async function changeIsStopGroupPush(chatId:string, isStopGroupPush:boolean) {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data: { is_stop_group_push:isStopGroupPush } })
}

export async function changeCourseNo(chatId:string, courseNo:number) {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data:{ course_no:courseNo } })
}

export async function changePhone(chatId:string, phone:string) {
  throw ('not implement')
}

export async function setChatLanguage(chatId: string, language: string): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasInstance()
  // use a raw Mongo update to set the nested field directly without reading first
  // note: this uses Prisma client's $runCommandRaw to execute a Mongo update
  await mongoClient.$runCommandRaw({
    update: 'chat',
    updates: [
      {
        q: { _id: chatId },
        u: { $set: { 'chat_state.state.language': language } },
        upsert: false,
      },
    ],
  })
}

export async function updatePayTime(chatId:string, time:string): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data:{ pay_time:dayjs(time).toDate() } })
}

export async function changeNextStage(chatId:string, stage:Node) {
  'use server'
  throw ('not implement')
  // const mongoClient = AdminPrismaMongoClient.getMoerOverseasInstance()
  // const chatInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
  // if (!chatInfo) {
  //   throw '没有找到这个人'
  // }
  // const mongoConfigInstance = PrismaMongoClient.getConfigInstance()
  // const botInfo = await mongoConfigInstance.config.findFirst({ select:{ address:true }, where:{ wechatId:chatInfo.wx_id } })
  // if (!botInfo) {
  //   throw '没有找到对应的机器人配置'
  // }
  // const address = botInfo.address
  // await axios(`${address}/test/event`, {
  //   method:'POST',
  //   data:{
  //     chatId: chatId,
  //     name: ITestEventType.ChangeNextStage,
  //     stage: stage
  //   }
  // }).then((res) => {
  //   if (res.data.code != 200) {
  //     throw res.data.msg
  //   }
  // })
}

export interface baseResponse {
  code:number
  msg:string
}

export async function getChatByCourseWeekRange(
  minCourseWeek: number,
  maxCourseWeek: number
) {
  const mongoClient = AdminPrismaMongoClient.getMoerOverseasInstance()
  const chatList = await mongoClient.chat.findMany({
    where: {
      course_no: {
        gte: minCourseWeek,
        lte: maxCourseWeek,
      },
    },
  })
  return chatList
}

function transferChatIntoUserData(chat:chat):MoerUserData {
  return {
    id: chat.id,
    course_no: chat.course_no,
    contact: chat.contact,
    wx_id: chat.wx_id,
    is_human_involved: chat.is_human_involved,
    chat_state: {
      nextStage: chat.chat_state.nextStage,
      state: chat.chat_state.state as Record<string, boolean | undefined>,
      userSlots: chat.chat_state.userSlots as Record<string, contentWithFrequency>
    },
    is_stop_group_push: chat.is_stop_group_push,
    phone: chat.phone_number,
    pay_time: chat.pay_time
  }
}