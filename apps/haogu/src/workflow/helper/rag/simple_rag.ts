import logger from 'model/logger/logger'
import { RAGHelper } from 'service/rag/rag'
import ElasticSearchService, { IElasticEmbeddingRes } from 'model/elastic_search/elastic_search'
import { ragHelperClient } from '../../../config/instance/rag_instance'
import { DataService } from '../getter/get_data'

export class SimpleRag {

  public static nonRAGMatchIndex = 'moer_non_rag_queries_2048d'
  public static index = 'haogu_general_rag'

  public static async search(inputQuery: string, chat_id: string, round_id: string) {
    const maxQANum = 5
    inputQuery = await ragHelperClient.mergeQuery(inputQuery, chat_id)
    const isNeedRAG = await this.isNeedRAG(inputQuery, chat_id)
    if (!isNeedRAG) {
      logger.trace({ chat_id: chat_id }, '当前问题无需rag:', inputQuery)
      return ''
    }

    const topic = ['']
    const subQueries = await ragHelperClient.queryReWriteWithChatHistory(inputQuery, topic, chat_id, round_id)

    const embeddingResults = await Promise.all(subQueries.map((subQuery) =>  this.embeddingSearch(chat_id, subQuery)))

    // 获取检索的 QA 对
    const retrievedQAs = RAGHelper.removeDuplicateQA(embeddingResults
      .flat()
      .map((item) => {
        return {
          q: item.pageContent,
          a: item.metadata.a
        }
      }))

    let reRankResults = await RAGHelper.reRank(subQueries.join(), retrievedQAs, { top_n: maxQANum, threshold: 0.1 })
    logger.trace('reRank Results:', JSON.stringify(reRankResults, null, 2))


    // 限制召回结果数
    if (reRankResults.length > maxQANum) {
      reRankResults = reRankResults.slice(0, maxQANum)
    }

    return reRankResults.map((qa) => `- 问题：${qa.q}\n  - 答案：${qa.a}`).join('\n')
  }

  private static async embeddingSearch(chat_id: string, query: string): Promise<IElasticEmbeddingRes[]> {
    // 获取要查询的文档
    const queryDocs = await this.getQueryDocs(chat_id)

    // 构建查询 filter
    const filter = {
      bool:{
        must:[
          {
            terms: {
              'metadata.doc': queryDocs
            }
          }
        ]
      }
    }

    // Embedding Search
    return await ElasticSearchService.embeddingSearch(
      this.index,
      query,
      10,
      0.74,
      filter
    )
  }

  public static async getQueryDocs(chat_id: string): Promise<string[]> {

    const commonDoc = [
      '常规问题全局',
      'PC端操作手册',
      '领航版操作手册'
    ]

    const beforeSales = [
      '售前问题'
    ]

    const afterSales = [
      '售后问题',
      '实战班介绍'
    ]

    const preCourseDoc = [
      ...commonDoc,
      ...beforeSales
    ]

    const day1Doc = [
      ...commonDoc,
      ...beforeSales,
      '第一天课程逐字稿'
    ]

    const day2Doc = [
      ...commonDoc,
      ...beforeSales,
      '第一天课程逐字稿',
      '第二天课程逐字稿'
    ]

    const day3Doc = [
      ...commonDoc,
      ...beforeSales,
      '第一天课程逐字稿',
      '第二天课程逐字稿',
      '第三天课程逐字稿'
    ]

    const day4Doc = [
      ...commonDoc,
      ...afterSales,
      '第一天课程逐字稿',
      '第二天课程逐字稿',
      '第三天课程逐字稿',
      '第四天课程逐字稿'
    ]

    const day5Doc = [
      ...commonDoc,
      ...afterSales,
      '第一天课程逐字稿',
      '第二天课程逐字稿',
      '第三天课程逐字稿',
      '第四天课程逐字稿',
      '第五天课程逐字稿'
    ]

    const day6Doc = [
      ...commonDoc,
      ...afterSales,
      '第一天课程逐字稿',
      '第二天课程逐字稿',
      '第三天课程逐字稿',
      '第四天课程逐字稿',
      '第五天课程逐字稿',
      '第六天课程逐字稿'
    ]

    if (await DataService.isInCourseTimeLine(chat_id, 'beforeCourse', 1)) {
      return preCourseDoc
    } else if (await DataService.isInCourseTimeLine(chat_id, 'beforeCourse', 2)) {
      return day1Doc
    } else if (await DataService.isInCourseTimeLine(chat_id, 'beforeCourse', 3)) {
      return day2Doc
    } else if (await DataService.isInCourseTimeLine(chat_id, 'beforeCourse', 4)) {
      return day3Doc
    } else if (await DataService.isInCourseTimeLine(chat_id, 'beforeCourse', 5)) {
      return day4Doc
    } else if (await DataService.isInCourseTimeLine(chat_id, 'beforeCourse', 6)) {
      return day5Doc
    } else {
      return day6Doc
    }
  }



  private static async isNeedRAG(inputQuery: string, chat_id: string) {
    const queryParts = inputQuery.split(/[，。\s]+/).filter((part) => part.trim() !== '')
    const unmatchedParts: string[] = []

    for (const part of queryParts) {
      const ragResults = await ElasticSearchService.embeddingSearch(this.nonRAGMatchIndex, part, 1, 0.8)

      if (ragResults.length === 0) {
        // 没有搜索到了，输出，后面要rag
        unmatchedParts.push(part)
      } else {
        logger.trace({ chat_id: chat_id }, `被删除的部分query "${part}":`, ragResults)
      }
    }

    return unmatchedParts.length > 0 && RAGHelper.isNeedRAG(unmatchedParts.join())
  }

}