import logger from 'model/logger/logger'
import { chatStateStoreClient } from '../../config/instance/base_instance'
import { IChattingFlag } from '../../config/manifest'
import { sleep } from 'openai/core'
import { getUserId } from 'config/chat_id'
import { DataService } from '../helper/getter/get_data'
// import { SalesCase } from '../../helper/rag/sales_case'
import { ActionInfo } from 'service/agent/stage'
import { SendMessageType } from 'service/visualized_sop/common_sender/type'
import { commonMessageSender } from '../../config/instance/send_message_instance'

export class PostAction {
  public static getPeiPaoCourseIntro(): string {
    return `陪跑营卖点:
- 陪跑营核心是中神通老师带队直接带你30天迈过从知道思路到拿到结果过程中所有的问题
- 整个过程是1对1私人订制您的方案，陪跑过程分为3个阶段。
  - 第一阶段（0-3天）：围绕账号定位账号搭建，根据你规模和你的生意订制你的账号定位。
  - 第二阶段（3-15天）：打造人设，拍摄剪辑。手把手带你用532 内容生产法则，真正打造能获客的账号。
  - 第三阶段（15-30天）：全矩阵的，直播引流，私域运营。
- 除了1对1陪跑服务外，还配套提供以下福利：
  - 365天答疑：30天结束后，咱们的陪跑群还是继续存在，365天给您答疑，平台规则发生的变化，后续落地遇到的任何问题都可以陪伴解答。
  - 37节的获客实操课程，以及团购直播课程：除了私人订制的陪跑服务之外，陪跑营还赠送37节的获客实操课程，以及团购直播课程，这些课程都是中神通老师自己讲的课程，都是非常落地的干货。支持永久回放。
  - AI数字人和AI爆店系统：在整个陪跑过程，针对出镜头难，文案能力不行的问题，赠送AI数字人和AI爆店系统，解决这2个问题。基本去掉了所有做起来账号的技术难点了。要的就是咱一定要做起来的决心了！只要您想做，我们就有信心帮您拿到结果。
  - 3天2夜的线下课：最后重磅！除了线上对接之外，2980还赠送3天2夜的线下课（机票酒店自理），直接现场带您实操落地！`
  }

  static async sendCourseIntro(chat_id: string) {
    const user_id = getUserId(chat_id)

    const actionInfo: ActionInfo = {
      guidance: PostAction.getPeiPaoCourseIntro(),
    }

    if ((await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)).is_send_course_intro) { return actionInfo }

    actionInfo.callback = async () => {
      await sleep(2000)

      await chatStateStoreClient.update(chat_id, { state: <IChattingFlag> { is_send_course_intro: true } })
    }
    return actionInfo
  }

  public static async sendInvitation() {
    const purchaseLink = 'http://3tG7eG1l.w.njqzlive.com/p/IuVJkRcT'
    const actionInfo: ActionInfo = { guidance: `务必向客户发送下单链接：${purchaseLink}，并邀约客户购买` }
    return actionInfo
  }

  // public static async sendCaseImage(chat_id: string): Promise<IActionInfo> {
  //   const user_id = getUserId(chat_id)
  //   const searchResult = await SalesCase.searchByChatId(chat_id, [SalesCase.peipaoyingDoc])
  //   const revenueTmp = await UserSlots.getUserSlotSubTopicContent(chatStateStoreClient, chat_id, '基本信息', '年营业额')
  //   const revenue = revenueTmp === '' ? '20万' : revenueTmp
  //   const actionInfo: IActionInfo = { guidance: `案例信息：学员学习前：${revenue}左右年营业额（营业额数值可以随机一点），学员学习后：营业额有提升但不要太夸张` }
  //
  //   if (!searchResult) { return actionInfo }
  //
  //   const chatHistory = await chatHistoryServiceClient.formatHistoryOnRole(chat_id, 'assistant', 5)
  //
  //   if (chatHistory.includes(`${searchResult.topic}案例1`)) { return actionInfo }
  //
  //   const sendImages = async () => {
  //     await sleep(5000)
  //     const images = searchResult.images.slice(0, 3)
  //
  //     for (let i = 0; i < images.length; i++) {
  //       const image = images[i]
  //       await sleep(1000)
  //
  //       await wecomMessageSender.sendById({
  //         user_id: user_id,
  //         chat_id: chat_id,
  //         ai_msg: `[${searchResult.topic}案例${i + 1}]`,
  //         send_msg: {
  //           type: IWecomMsgType.Image,
  //           url: image,
  //         },
  //       })
  //     }
  //   }
  //
  //   actionInfo.guidance = `案例信息：${searchResult.description}`
  //   actionInfo.callback = sendImages
  //   return actionInfo
  // }

  public static async provideDepositPlan() {
    const actionInfo: ActionInfo = { guidance: '主动提出可以先付1000定金，后续进入陪跑营之后再补齐，现在使用花呗还可以分期付款' }
    return actionInfo
  }

  public static async reaskAnotherDay(chat_id: string) {
    logger.trace({ chat_id: chat_id }, '客户保留名额，次日询问')
    // await SilentReAsk.schedule(TaskName.reask_another_day, chat_id, 24 * 60 * 60 * 1000, {}, { auto_retry: true }) // 24小时后提醒客户
    return { guidance: '' } as ActionInfo
  }

  /**
   * 返回day5 9 13等课程时间的**开课时间**
   * @param chat_id
   */
  public static async getNewCourseStartDayDiff(chat_id: string) {
    const currentTime = await DataService.getCurrentTime(chat_id)
    let day = currentTime.day
    // day = 0 为 day 5开课，day = 5为当天开课
    if (day == 0) {
      return 5
    }
    if (day > 1) {
      day = day - 1
    }
    const multiple = Math.ceil(day / 4)
    return  multiple * 4 - currentTime.day + 1
  }

  public static async askQuestionOne(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text: `我们先来对您的股票段位测评做一组测试，仔细回答我们会针对您的情况为您服务，请直接回复字母，比如：A B C D
1. 您的江湖身份：
A. 男青年（热血上头型🧨）
B. 男中年（拼搏稳健型📈）
C. 老年退休（沉稳低调型🧘）
D. 女股侠（清醒理智型🌸）

2. 您征战股市多久：
A. 不到1年，刚入江湖
B. 1-3年，有点经验
C. 3-5年，老鸟但浮躁
D. 5年以上，资深沉淀

3. 您师从哪个门派？
A. 独门自创（纯凭感觉）
B. 江湖股吧（跟风信群）
C. 学术研读（书上高手）
D. 各种机构（培训常客）`,
      description: '挖需第一组问题，询问投资身份类型，进入股市时长，投机理念，三道题，每道题ABCD四个选项，A到D分数逐渐增高，D为最高分'
    }, {
      force:true
    })
    return { guidance: '结束后续回复', callback: async() => {} }
  }

  public static async askQuestionTwo(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text: `收到～ 请直接回字母回答接下来一组问题，比如：A B C D

您惯用的炒股招式是：
A. 短线追涨杀跌（快进快出）
B. 中长线持股（耐心等待）
C. 消息面驱动（谁热买谁）
D. 随机操作（靠运气）

您的出手资金一般是多少？
A. 不到5万（新手尝试）
B. 5–30万（实操练级）
C. 30–100万（认真投资）
D. 100万以上（重仓对决）

您过去的炒股战绩是？
A. 稳定盈利（高手风范）
B. 时赚时亏（过山车）
C. 基本持平（佛系观望）
D. 经常亏损（割肉专业户）`,
      description: '挖需第二组问题，询问炒股技巧，炒股资金情况，过往炒股战绩,三道题，每道题ABCD四个选项，A到D分数逐渐增高，D为最高分'
    }, {
      force:true
    })
    return { guidance: '结束后续回复', callback: async() => {} }
  }

  public static async askQuestionThree(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text: `收到～具体测评结果公布还差最后一步，请继续直接回字母回答第二组问题，比如：A B C D

您最大的交易困扰是？
A. 不会选股，瞎买
B. 不会止损，爱扛单
C. 资金曲线乱，心情也乱
D. 没方法，全靠运气

您更偏好的收益方式？
A. 快进快出，追热点
B. 稳扎稳打，选结构
C. 中间摇摆，无法坚定
D. 想稳住但总忍不住

炒股对你来说是为了？
A. 一夜暴富
B. 赚点零花、补贴生活
C. 稳定理财、退休准备
D. 逆人性修炼自我纪律`,
      description: '挖需第三组问题，询问最大交易困扰，看什么判断买点，偏好的收益方式，炒股理由，三道题，每道题ABCD四个选项，A到D分数逐渐增高，D为最高分'
    }, {
      force:true
    })
    return { guidance: '结束后续回复', callback: async() => {} }
  }

  public static async askResultLevel1(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text: `段位一｜菜鸟闯荡型
结果解读：
 你是江湖中的“初生牛犊”，刚入股市江湖不久，主要靠感觉或消息出手，缺乏一套可重复的交易体系。优点是学习接受度高，但缺点是容易冲动下单、被情绪带节奏。
典型痛点：
1. 不知道如何科学选股
2. 频繁追涨杀跌，容易买在高点卖在低点
3. 没有止损规则，亏损扩大

行动建议桥接：
 你现在正处在快速提升的黄金阶段，如果尽早建立“结构交易”方法论，可以少走3-5年的弯路。我们6天的课程就是帮你搭建这个体系，从0到能看懂并执行稳定的交易。`,
      description: '股票段位评测结束了，你的评测结果是菜鸟闯荡型'
    })
    await chatStateStoreClient.update(chat_id, {
      state:<IChattingFlag>{
        is_finish_stock_ranking_assessment:true
      }
    })
    return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  }
  public static async askResultLevel2(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text: `段位二｜小试牛刀型
结果解读：
 你已经有一定的股市经验，可能做过短线或中长线，有时能赚钱，但战绩波动大。你的交易招式多变，但缺乏统一逻辑，容易在不同策略之间摇摆。
典型痛点：
1. 选股靠热点、买卖靠感觉
2. 盈亏交替，资金曲线像过山车
3. 缺乏长期稳定盈利的纪律体系

行动建议桥接：
 你需要一套“固定打法”——既能锁定高胜率机会，又能严格控制风险。6天课程会让你掌握“1涨2回3主升”结构法，帮你稳定执行，提升胜率。`,
      description: '股票段位评测结束了，你的评测结果是小试牛刀型'
    })
    await chatStateStoreClient.update(chat_id, {
      state:<IChattingFlag>{
        is_finish_stock_ranking_assessment:true
      }
    })
    return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  }
  public static async askResultLevel3(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text: `段位三｜进阶修炼型
结果解读：
 你是股市里的“老江湖”，有稳定的分析方法，但容易在执行上走形，或者在行情波动时失去耐心。你已经有一定结构意识，但盈利还不够稳定。
典型痛点：
1. 理论懂很多，实盘执行打折扣
2. 资金曲线容易受情绪影响
3. 缺少长期跟踪复盘的习惯

行动建议桥接：
 你需要的不是更多知识，而是让已有方法落地执行。实战训练班会帮你通过案例、作业和一对一答疑，把理论转化为稳定盈利的实战能力。`,
      description: '股票段位评测结束了，你的评测结果是进阶修炼型'
    })
    await chatStateStoreClient.update(chat_id, {
      state:<IChattingFlag>{
        is_finish_stock_ranking_assessment:true
      }
    })
    return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  }
  public static async askResultLevel4(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text: `段位四｜系统投资型
结果解读：
 你是理性派投资者，方法较成熟，能根据大势和结构进行操作。但可能在资金管理、买卖节奏、行情应对等细节上，还存在优化空间。
典型痛点：
1. 盈利稳定但成长速度慢
2. 有时错过最佳买点或卖点
3. 对结构判断的执行力还可提升

行动建议桥接：
 你需要更精准的结构选股模型和严格的买卖执行方案。我们的课程会帮你在现有体系上再升级，让资金曲线更陡、更稳。`,
      description: '股票段位评测结束了，你的评测结果是系统投资型'
    })
    await chatStateStoreClient.update(chat_id, {
      state:<IChattingFlag>{
        is_finish_stock_ranking_assessment:true
      }
    })
    return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  }
  public static async askResultLevel5(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text: `段位五｜理性操盘型
结果解读：
 你是成熟操盘手，交易逻辑清晰、纪律性强，已经能稳定盈利。但如果想在资金规模、收益率和策略多样性上再突破，需要吸收更多实战验证过的高效方法。
典型痛点：
1. 策略较固定，适应性有待增强
2. 缺少与高手切磋和验证的机会
3. 对部分结构机会利用不够充分

行动建议桥接：
 你可以用课程作为“复盘+优化”的平台，把已有的体系与我们的结构交易模型对比升级，同时参与高水平实战训练，拓展盈利上限。`,
      description: '股票段位评测结束了，你的评测结果是理性操盘型'
    })
    await chatStateStoreClient.update(chat_id, {
      state:<IChattingFlag>{
        is_finish_stock_ranking_assessment:true
      }
    })
    return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  }

  public static async sendEconomicCurve(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendMsg(chat_id, [{
      type: SendMessageType.image,
      url:'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/haogu/asset/expert_capital_curve.png',
      description:'高手资金曲线图片'
    }, {
      type:SendMessageType.text,
      text:'你看，这就是高手的资金曲线——平稳向上，没有剧烈的大起大落。交易的终局不是靠一次暴富改变命运，而是靠长期稳定复利，让资金曲线越来越漂亮。股市是“久富平台”，不是“暴富平台”。真正爽的交易，是每一次买卖都胸有成竹，利润是努力和学习的自然结果；而不是今天赚明天亏，情绪跟着账户上蹿下跳。你可以对照看下你的投资账户资金曲线，也可一发给我来看看🫣',
      description:'介绍高手资金曲线'
    }, {
      type:SendMessageType.text,
      text:'我们好人好股，是唯一一家把股民当成交易员进行专业赋能的培训机构。我们不推荐股票，而是授人以渔，真心希望教会学员一套稳定赚钱的交易方法，让每一次操作都有章可循。很开心在茫茫人海中遇到你！接下来，我们将开启一段6天的股民开悟之旅，让你彻底搞明白——你在股市赚的是什么钱、如何持续稳定地赚钱。📅 明天第一节课，记得准时来上课，我们直播见！',
      description:'介绍高手资金曲线后介绍好人好股'
    }])
    await chatStateStoreClient.update(chat_id, {
      state:<IChattingFlag>{
        after_bonding:true
      }
    })
    return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  }
}