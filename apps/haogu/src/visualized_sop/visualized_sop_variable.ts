import { HandleActionOption } from 'service/visualized_sop/visualized_sop_processor'

export const conditionJudgeMap:Record<string, ((params:{chatId:string;userId:string})=>Promise<boolean>)> = {
  '第一节课到课': async() => {
    return false
  },
  '第一节课完课': async() => {
    return false
  },
  '第二节课到课': async() => {
    return false
  },
  '第二节课完课': async() => {
    return false
  },
  '第三节课到课': async() => {
    return false
  },
  '第三节课完课': async() => {
    return false
  },
  '第四节课到课': async() => {
    return false
  },
  '第四节课完课': async() => {
    return false
  },
  '第五节课到课': async() => {
    return false
  },
  '第五节课完课': async() => {
    return false
  },
  '第六节课到课': async() => {
    return false
  },
  '第六节课完课': async() => {
    return false
  }
}

export const textVariableMap:Record<string, (params:{chatId:string;userId:string})=> Promise<string>> = {
  '用户昵称': async({ chatId:string }) => {
    return ''
  },
  '第一节课直播链接': async({ chatId:string }) => {
    return ''
  },
  '第二节课直播链接': async({ chatId:string }) => {
    return ''
  },
  '第三节课直播链接': async({ chatId:string }) => {
    return ''
  },
  '第四节课直播链接': async({ chatId:string }) => {
    return ''
  },
  '第五节课直播链接': async({ chatId:string }) => {
    return ''
  },
  '第六节课直播链接': async({ chatId:string }) => {
    return ''
  }
}

export const actionCustomMap:Record<string, (params:{chatId:string;userId:string;opt:HandleActionOption})=> Promise<void>> = {
}

export const linkSourceVariableTagMap:Record<string, (params:{chatId:string;userId:string})=>Promise<string>> = {
}
